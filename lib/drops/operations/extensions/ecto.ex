defmodule Drops.Operations.Extensions.Ecto do
  @behaviour Drops.Operations.Extension

  @moduledoc """
  Ecto extension for Operations.

  This extension adds Ecto-specific functionality to Operations modules when
  a repo is configured. It provides:

  - Changeset validation pipeline
  - `changeset/1` and `persist/1` functions
  - Phoenix.HTML.FormData protocol support for Success/Failure structs
  - Schema error conversion to changeset errors
  - Automatic casting support for Ecto schemas (cast: true by default)
  - Simplified changeset creation leveraging Drops schema casting

  The extension is automatically enabled when the `:repo` option is provided.

  ## Automatic Casting

  When using Ecto schemas in operations, the extension automatically enables
  casting support (`cast: true`) by default. This means that string inputs
  will be automatically cast to the appropriate Ecto types (e.g., "42" to 42
  for integer fields). You can still override this by explicitly setting
  `cast: false` in your schema options.

  ## Examples

      # Automatic casting enabled by default
      operation type: :command do
        schema(MyApp.User)  # cast: true is applied automatically

        def execute(context), do: {:ok, context}
      end

      # Explicitly disable casting if needed
      operation type: :command do
        schema(MyApp.User, cast: false)

        def execute(context), do: {:ok, context}
      end
  """

  @doc """
  Callback for creating an Ecto changeset from context.
  Receives context map and should return {:ok, context_with_changeset} or error tuple.
  """
  @callback changeset(context :: map()) :: {:ok, map()} | {:error, any()}

  @doc """
  Callback for validating an Ecto changeset.
  Receives context map with changeset and should return the validated changeset.
  """
  @callback validate_changeset(context :: map()) :: Ecto.Changeset.t()

  @doc """
  Callback for getting the struct to use for changeset creation.
  Receives context map and should return an Ecto struct
  """
  @callback get_struct(context :: map()) :: struct()

  @optional_callbacks changeset: 1, validate_changeset: 1, get_struct: 1

  @impl true
  def enabled?(opts) do
    Keyword.has_key?(opts, :repo) && !is_nil(opts[:repo])
  end

  @impl true
  def extend_operation(opts) do
    quote location: :keep do
      @behaviour Drops.Operations.Extensions.Ecto

      import Ecto.Changeset

      @schema_opts Keyword.merge(Module.get_attribute(__MODULE__, :schema_opts, []),
                     cast: true
                   )

      def ecto_schema, do: schema().meta[:source_schema]

      def repo, do: unquote(opts[:repo])

      def validate(%{changeset: changeset} = context) do
        case validate_changeset(%{context | changeset: %{changeset | action: :validate}}) do
          %{valid?: true} = changeset ->
            {:ok, %{context | changeset: %{changeset | action: nil}}}

          changeset ->
            {:error, changeset}
        end
      end

      defp cast_embedded_fields(changeset, embedded_fields, params) do
        Enum.reduce(embedded_fields, changeset, fn field, acc ->
          if Map.has_key?(params, field) do
            cast_embed(acc, field)
          else
            acc
          end
        end)
      end

      def get_struct(_context) do
        struct(ecto_schema())
      end

      def changeset(%{params: params} = context) do
        struct = get_struct(context)
        schema_module = ecto_schema()
        embedded_fields = schema_module.__schema__(:embeds)

        changeset = change(struct, params)
        changeset = cast_embedded_fields(changeset, embedded_fields, params)

        {:ok, Map.put(context, :changeset, changeset)}
      end

      def validate_changeset(%{changeset: changeset}) do
        changeset
      end

      def insert(changeset) do
        repo().insert(%{changeset | action: :insert})
      end

      def update(changeset) do
        repo().update(%{changeset | action: :update})
      end
    end
  end

  @impl true
  def extend_unit_of_work(uow, _mod, opts) do
    schema_meta = Keyword.get(opts, :schema_meta, %{})
    default_schema_meta = Map.get(schema_meta, :default, %{})
    has_ecto_schema = Map.get(default_schema_meta, :ecto_schema, false)

    if has_ecto_schema do
      uow |> Drops.Operations.UnitOfWork.after_step(:prepare, :changeset)
    else
      uow
    end
  end

  def has_ecto_schema?(operation_module) do
    schema = operation_module.schema()
    !is_nil(schema.meta[:source_schema])
  end
end
